# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# Dependencies
/node_modules
/.turbo
/.idea
.pnp
.pnp.js

/apps/backend/public/temp
# Local env files
.env
backend.env
db.env
frontend.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Testing
coverage
tmp.txt
report.servicecopy.ts
# Turbo
.turbo

# Vercel
.vercel

# Build Outputs
.next/
out/
build
dist


# Debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Misc
.DS_Store
*.pem

# dummy server
db.json
