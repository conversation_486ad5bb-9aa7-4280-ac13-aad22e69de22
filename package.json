{"name": "academic360", "private": true, "scripts": {"build": "turbo build", "dev": "turbo dev", "lint": "turbo lint", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "prepare": "husky"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,jsx,ts,tsx,json,css,scss,md}": ["prettier --write", "eslint --fix"]}, "devDependencies": {"@types/file-saver": "^2.0.7", "@types/js-cookie": "^3.0.6", "@types/jwt-decode": "^2.2.1", "@types/multer": "^1.4.12", "add": "^2.0.6", "autoprefixer": "^10.4.20", "husky": "^9.1.7", "postcss": "^8.4.49", "prettier": "^3.2.5", "rollup": "^4.34.4", "tailwindcss": "^3.4.17", "turbo": "^2.3.3", "typescript": "5.5.4", "vite": "^6.0.6"}, "engines": {"node": ">=18"}, "packageManager": "npm@10.9.2", "workspaces": ["apps/*"], "dependencies": {"@ant-design/icons": "^6.0.0", "@clerk/clerk-react": "^5.21.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@react-oauth/google": "^0.12.1", "@reduxjs/toolkit": "^2.6.1", "@shadcn/ui": "^0.0.4", "@tanstack/react-table": "^8.20.6", "@types/socket.io": "^3.0.1", "antd": "^5.24.9", "axios": "^1.7.9", "cmdk": "^1.0.4", "country-state-city": "^3.2.1", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "drizzle-orm": "^0.38.3", "express-rate-limit": "^7.5.0", "file-saver": "^2.0.5", "framer-motion": "^11.18.0", "install": "^0.13.0", "js-cookie": "^3.0.5", "jspdf": "^2.5.2", "jspdf-autotable": "^3.8.4", "jwt-decode": "^4.0.0", "lodash": "^4.17.21", "lucide-react": "^0.503.0", "npm": "^11.2.0", "react-day-picker": "^9.5.1", "react-icons": "^5.5.0", "react-redux": "^9.2.0", "react-toastify": "^11.0.5", "recharts": "^2.15.3", "swiper": "^11.2.1", "tailwind-scrollbar": "^3.1.0", "whatwg-url": "^14.1.1", "xlsx": "^0.18.5", "zeptomail": "^6.2.1", "zod": "^3.24.1", "zustand": "^5.0.3"}, "optionalDependencies": {"@rollup/rollup-linux-x64-musl": "4.9.5"}}