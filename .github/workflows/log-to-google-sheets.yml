name: Log GitHub Activities to Google Sheets

on:
  push:
    branches:
      - "**"
  issues:
    types: [opened, edited, closed, labeled]
  pull_request:
    types: [opened, edited, closed]
  create:
  delete:
  workflow_dispatch:

jobs:
  log_activity:
    runs-on: ubuntu-latest

    steps:
      - name: Log Activity to Google Sheets
        env:
          WEB_APP_URL: ${{ secrets.GOOGLE_SHEETS_WEBHOOK }}
        run: |
          # Load the GitHub event payload
          ACTION="${{ github.event.action || 'push' }}"
          GITHUB_EVENT_PATH=${GITHUB_EVENT_PATH:-/github/workflow/event.json}

          # Determine the branch name
          if [[ "${ACTION}" == "push" || "${ACTION}" == "create" || "${ACTION}" == "delete" ]]; then
            BRANCH="${{ github.ref }}"  # Extract branch from ref for push/create/delete events
            BRANCH="${BRANCH#refs/heads/}"  # Remove "refs/heads/" prefix
          elif [[ "${ACTION}" == "pull_request" ]]; then
            BRANCH="${{ github.event.pull_request.head.ref }}"  # Extract branch for pull request source branch
          else
            BRANCH="N/A"  # No branch for issue events
          fi

          # Determine the resource type
            if [[ "${ACTION}" == "push" || "${ACTION}" == "create" || "${ACTION}" == "delete" ]]; then
            RESOURCE="Branch"
            elif [[ "${ACTION}" == "pull_request" ]]; then
            RESOURCE="Pull Request"
            elif [[ "${ACTION}" == "issues" ]]; then
            RESOURCE="Issue"  # Explicitly set resource to "Issue" for any issue event
            fi

          # Extract description
            DESCRIPTION=""
            if [[ "$ACTION" == "push" ]]; then
                DESCRIPTION=$(jq -r '.commits[].message' "$GITHUB_EVENT_PATH" | tr '\n' ' ' | sed 's/ $//')
                [[ -z "$DESCRIPTION" ]] && DESCRIPTION="No commit messages available"
            else
                DESCRIPTION=$(jq -r '.pull_request.title // .issue.title // "No details available for this event"' "$GITHUB_EVENT_PATH")
            fi


          # Debug logs
          echo "Action: $ACTION"
          echo "Branch: $BRANCH"
          echo "Resource: $RESOURCE"
          echo "Description: $DESCRIPTION"
          echo "User: ${{ github.actor }}"
          echo "Webhook URL: $WEB_APP_URL"

          # Send data to Google Sheets
          curl -X POST -H "Content-Type: application/json" \
              -d "{\"action\": \"$ACTION\", \"user\": \"${{ github.actor }}\", \"description\": \"$DESCRIPTION\", \"branch\": \"$BRANCH\", \"resource\": \"$RESOURCE\"}" \
              "$WEB_APP_URL"
