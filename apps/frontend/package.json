{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@hookform/resolvers": "^3.10.0", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-alert-dialog": "^1.1.7", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.2", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.3", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-scroll-area": "^1.2.6", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.4", "@radix-ui/react-tooltip": "^1.1.6", "@tanstack/react-query": "4.36.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "country-state-city": "^3.2.1", "date-fns": "^3.6.0", "date-fns-tz": "^3.2.0", "input-otp": "^1.4.2", "lodash": "^4.17.21", "lucide-react": "^0.469.0", "next-themes": "^0.4.4", "react": "^18.3.1", "react-datepicker": "^8.4.0", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.54.2", "react-resizable-panels": "^2.1.7", "react-router-dom": "^7.1.1", "recharts": "^2.15.4", "socket.io-client": "^4.8.1", "sonner": "^1.7.2", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.2"}, "optionalDependencies": {"@rollup/rollup-linux-x64-musl": "4.9.5"}, "overrides": {"react": "^18.3.1", "react-dom": "^18.3.1"}, "devDependencies": {"@types/lodash": "^4.17.15", "@types/node": "^22.10.3", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@types/recharts": "^1.8.29", "@vitejs/plugin-react": "^4.3.4", "add": "^2.0.6", "autoprefixer": "^10.4.20", "eslint": "^9.17.0", "eslint-config": "*", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.14.0", "husky": "^9.1.7", "postcss": "^8.4.49", "tailwindcss": "^3.4.17", "typescript": "~5.6.2", "typescript-eslint": "^8.18.2", "vite": "^6.0.5"}}