.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
  
  border-radius: 30px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f0f0f0;
  border-radius: 10px; 
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #bdbdbd;
  border-radius: 30px; 
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #7b7b7b;
}

.custom-scrollbar::-webkit-scrollbar-button {
  display: none;
}

/* Purple theme scrollbar */
.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(147, 51, 234, 0.3);
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgb(141, 28, 247);
}