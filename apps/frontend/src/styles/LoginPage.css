
  .app-container {
    background: linear-gradient(rgba(0, 0, 0, 0.9), rgba(0, 0, 0, 0.6)), url("@/assets/clgimg.png");
    
    background-size:cover;
    background-position: center;
    background-repeat: no-repeat;
    height: 100vh;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  
  .wave {
    position: fixed;
    bottom: 0;
    left: 0;
    height: 100%;
    z-index: -1;
  }
  
  .container {
   
    width: 100vw;
    height: 100vh;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-gap: 7rem;
    padding: 0 2rem;
  }
  

  
  .login-content {

    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
  }
  .img {
    position: relative;
 
    width : 600px;
    height: 600px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }
  .img-div1, .img-div2, .img-div3 {
    position: absolute;
    width: 50%;
    height: 40%;
    border-radius: 30px;
    border: 1px solid rgb(63, 63, 63);
    overflow: hidden;
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease-in-out;
   
}

.img-div1 {
    left: 13%;
    top: 31%;
    z-index: 100;
}

.img-div2 {
    left: 60%;
    top: 25%;
    width: 55%;
}

.img-div3 {
    left: 40%;
    top: 55%;
}

/* Hover Effects */
.img-div1:hover,
.img-div2:hover,
.img-div3:hover {
    transform: scale(1.1);
    filter: drop-shadow(0px 5px 10px #fff70046);
   
  
    transform: scale(1.1);
    z-index: 1001;
   
}


.img-div1:hover img,
.img-div2:hover img,
.img-div3:hover img {
  
    transition: transform 0.3s ease;
}

  .img img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
  }
  
  form {
    width: 100%;
    max-width: 400px;

    
    background: #6767671d;
    padding: 2.5rem 2rem;
    border: 2px solid #222;
    border-radius: 10px;
    backdrop-filter: blur(12px);
    box-shadow: 20px 20px 60px #00000041,inset -20px -20px 80px #dfdfdf1d;
  }
  
  .login-content img {
  
    height: 120px;
    margin-bottom:2rem ;
  }
  
  .login-content .title {
    margin: 30px 0;
    color: #333;
    text-transform: uppercase;
    font-size: 2.5rem;
  }
  
  .input-div {
    position: relative;
    display: grid;
    grid-template-columns: 7% 93%;
    margin: 20px 0;
    padding: 5px 0;
    border-bottom: 2px solid #d9d9d9;
  }
  
  .input-div.one {
    margin-top: 0;
  }
  
  .i {
    color: #ffffff;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  
  .i i {
    transition: 0.3s;
  }
  
  .input-div > div {
    position: relative;
    height: 45px;
  }
  
  .input-div > div > h5 {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #e4e4e4;
    font-size: 18px;
    transition: 0.3s;
  }
  
  .input-div:before,
  .input-div:after {
    content: '';
    position: absolute;
    bottom: -2px;
    width: 0%;
    height: 2px;
    background-color: #2182f9;
    transition: 0.4s;
  }
  
  .input-div:before {
    right: 50%;
  }
  
  .input-div:after {
    left: 50%;
  }
  
  .input-div.focus:before,
  .input-div.focus:after {
    width: 50%;
  }
  
  .input-div.focus > div > h5 {
    top: -5px;
    font-size: 15px;
  }
  
  .input-div.focus > .i > i {
    color: #2182f9;
  }
  
  .input-div > div > input {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    border: none;
    outline: none;
    background: none;
    padding: 0.5rem 0.7rem;
    font-size: 1.2rem;
    color: #e7e7e7;
    font-family: 'Poppins', sans-serif;
  }
  
  .input-div.pass {
    margin-bottom: 4px;
  }
  
  a {
    display: block;
    text-align: right;
    text-decoration: none;
    color: #d2d2d2;
    font-size: 0.9rem;
    transition: 0.3s;
  }
  
  a:hover {
    color: #0073ff;
  }
  
  .btn {
    display: block;
    width: 100%;
    height: 50px;
    border-radius: 25px;
    outline: none;
    border: none;
    background-image: linear-gradient(to right, #0073ff, #3882dd, #0073ff);
    background-size: 200%;
    font-size: 1.2rem;
    color: #fff;
    font-family: 'Poppins', sans-serif;
    text-transform: uppercase;
    margin: 1rem 0;
    cursor: pointer;
    transition: 0.5s;
  }
  
  .btn:hover {
    background-position: right;
  }
  
  @media screen and (max-width: 1024px) {
    .container {
      grid-gap: 4rem;
    }
  
    .img img {
      width: 400px;
    }
  
    form {
      width: 300px;
    }
  }
  
  @media screen and (max-width: 768px) {
    .container {
      grid-template-columns: 1fr;
      padding: 0 1rem;
    }
  
    .img {
      display: none;
    }
  
    .wave {
      display: none;
    }
  
    .login-content {
      justify-content: center;
    }
  
    form {
      width: 100%;
      padding: 1.5rem;
    }
  
    .login-content .title {
      font-size: 2rem;
    }
  
    .input-div > div > input {
      font-size: 1rem;
    }
  
    .btn {
      font-size: 1rem;
    }
  }
  
  @media screen and (max-width: 480px) {
    .login-content .title {
      font-size: 1.8rem;
    }
  
    .input-div {
      margin: 15px 0;
    }
  
    .input-div > div > h5 {
      font-size: 16px;
    }
  
    .btn {
      height: 45px;
      font-size: 0.9rem;
    }
  }
  