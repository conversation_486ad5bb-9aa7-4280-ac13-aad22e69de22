"use client";

import { TrendingUp } from "lucide-react";
import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Y<PERSON><PERSON><PERSON> } from "recharts";

import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart";

export interface ChartBarHorizontalCoursesProps {
  data: { course: string; students: number }[];
  title?: string;
  description?: string;
}

export function ChartBarHorizontalCourses({
  data,
  title = "Course-wise Student Stats",
  description = "Showing stats for selected academic year",
}: ChartBarHorizontalCoursesProps) {
  const chartConfig: ChartConfig = {
    students: {
      label: "Students",
      color: "var(--chart-1)",
    },
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent>
        <ChartContainer config={chartConfig}>
          <Bar<PERSON>hart
            accessibilityLayer
            data={data}
            layout="vertical"
            margin={{ left: -20 }}
            height={Math.max(400, data.length * 32)}
          >
            <XAxis type="number" dataKey="students" hide />
            <YAxis
              dataKey="course"
              type="category"
              tickLine={false}
              tickMargin={10}
              axisLine={false}
            />
            <ChartTooltip
              cursor={false}
              content={<ChartTooltipContent hideLabel />}
            />
            <Bar dataKey="students" fill="var(--color-students)" radius={5} />
          </BarChart>
        </ChartContainer>
      </CardContent>
      <CardFooter className="flex-col items-start gap-2 text-sm">
        <div className="flex gap-2 leading-none font-medium">
          Trending up by 5.2% this month <TrendingUp className="h-4 w-4" />
        </div>
        <div className="text-muted-foreground leading-none">
          Showing total students for all courses
        </div>
      </CardFooter>
    </Card>
  );
} 