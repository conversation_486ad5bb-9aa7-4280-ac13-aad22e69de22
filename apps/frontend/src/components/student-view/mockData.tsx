export interface ReportData {
    rollNo: number;
    semester: number;
    year: number;
    fullMarks: number;
    marksObtained: number;
    semesterCredit: number;
    sgpa: number;
    cumulativeCredit: number;
    cgpa: number;
    letterGrade: string;
    remarks: string;
  }
  
  export const mockData: ReportData[] = [
    {
      rollNo: 1,
      semester: 1,
      year: 2023,
      fullMarks: 500,
      marksObtained: 450,
      semesterCredit: 20,
      sgpa: 9.0,
      cumulativeCredit: 40,
      cgpa: 8.9,
      letterGrade: "A",
      remarks: "Excellent",
    },
    {
      rollNo: 2,
      semester: 2,
      year: 2023,
      fullMarks: 500,
      marksObtained: 420,
      semesterCredit: 18,
      sgpa: 8.5,
      cumulativeCredit: 38,
      cgpa: 8.4,
      letterGrade: "B+",
      remarks: "Good",
    },
    {
      rollNo: 3,
      semester: 1,
      year: 2022,
      fullMarks: 500,
      marksObtained: 460,
      semesterCredit: 20,
      sgpa: 9.2,
      cumulativeCredit: 40,
      cgpa: 9.0,
      letterGrade: "A",
      remarks: "Excellent",
    },
    {
      rollNo: 4,
      semester: 2,
      year: 2022,
      fullMarks: 500,
      marksObtained: 410,
      semesterCredit: 17,
      sgpa: 8.2,
      cumulativeCredit: 37,
      cgpa: 8.0,
      letterGrade: "B",
      remarks: "Good",
    },
    {
      rollNo: 5,
      semester: 1,
      year: 2021,
      fullMarks: 500,
      marksObtained: 400,
      semesterCredit: 16,
      sgpa: 8.0,
      cumulativeCredit: 36,
      cgpa: 7.9,
      letterGrade: "B",
      remarks: "Good",
    },
    {
      rollNo: 6,
      semester: 2,
      year: 2021,
      fullMarks: 500,
      marksObtained: 390,
      semesterCredit: 15,
      sgpa: 7.8,
      cumulativeCredit: 35,
      cgpa: 7.7,
      letterGrade: "B-",
      remarks: "Satisfactory",
    },
    {
      rollNo: 7,
      semester: 1,
      year: 2020,
      fullMarks: 500,
      marksObtained: 480,
      semesterCredit: 22,
      sgpa: 9.6,
      cumulativeCredit: 42,
      cgpa: 9.4,
      letterGrade: "A+",
      remarks: "Outstanding",
    },
    {
      rollNo: 8,
      semester: 2,
      year: 2020,
      fullMarks: 500,
      marksObtained: 450,
      semesterCredit: 20,
      sgpa: 9.0,
      cumulativeCredit: 40,
      cgpa: 8.9,
      letterGrade: "A",
      remarks: "Excellent",
    },
    {
      rollNo: 9,
      semester: 1,
      year: 2019,
      fullMarks: 500,
      marksObtained: 430,
      semesterCredit: 19,
      sgpa: 8.6,
      cumulativeCredit: 39,
      cgpa: 8.5,
      letterGrade: "B+",
      remarks: "Very Good",
    },
    {
      rollNo: 10,
      semester: 2,
      year: 2019,
      fullMarks: 500,
      marksObtained: 420,
      semesterCredit: 18,
      sgpa: 8.4,
      cumulativeCredit: 38,
      cgpa: 8.3,
      letterGrade: "B",
      remarks: "Good",
    },
    {
      rollNo: 11,
      semester: 1,
      year: 2018,
      fullMarks: 500,
      marksObtained: 470,
      semesterCredit: 21,
      sgpa: 9.4,
      cumulativeCredit: 41,
      cgpa: 9.3,
      letterGrade: "A+",
      remarks: "Excellent",
    },
    {
      rollNo: 12,
      semester: 2,
      year: 2018,
      fullMarks: 500,
      marksObtained: 440,
      semesterCredit: 19,
      sgpa: 8.8,
      cumulativeCredit: 39,
      cgpa: 8.7,
      letterGrade: "B+",
      remarks: "Good",
    },
    {
      rollNo: 13,
      semester: 1,
      year: 2017,
      fullMarks: 500,
      marksObtained: 400,
      semesterCredit: 16,
      sgpa: 8.0,
      cumulativeCredit: 36,
      cgpa: 7.9,
      letterGrade: "B",
      remarks: "Satisfactory",
    },
    {
      rollNo: 14,
      semester: 2,
      year: 2017,
      fullMarks: 500,
      marksObtained: 390,
      semesterCredit: 15,
      sgpa: 7.7,
      cumulativeCredit: 35,
      cgpa: 7.6,
      letterGrade: "B-",
      remarks: "Average",
    },
    {
      rollNo: 15,
      semester: 1,
      year: 2016,
      fullMarks: 500,
      marksObtained: 420,
      semesterCredit: 18,
      sgpa: 8.4,
      cumulativeCredit: 38,
      cgpa: 8.2,
      letterGrade: "B+",
      remarks: "Good",
    },
    {
      rollNo: 16,
      semester: 2,
      year: 2016,
      fullMarks: 500,
      marksObtained: 410,
      semesterCredit: 17,
      sgpa: 8.2,
      cumulativeCredit: 37,
      cgpa: 8.1,
      letterGrade: "B",
      remarks: "Good",
    },
    {
      rollNo: 17,
      semester: 1,
      year: 2015,
      fullMarks: 500,
      marksObtained: 460,
      semesterCredit: 20,
      sgpa: 9.1,
      cumulativeCredit: 40,
      cgpa: 9.0,
      letterGrade: "A",
      remarks: "Excellent",
    },
    {
      rollNo: 18,
      semester: 2,
      year: 2015,
      fullMarks: 500,
      marksObtained: 450,
      semesterCredit: 20,
      sgpa: 9.0,
      cumulativeCredit: 40,
      cgpa: 8.9,
      letterGrade: "A",
      remarks: "Excellent",
    },
    {
      rollNo: 19,
      semester: 1,
      year: 2014,
      fullMarks: 500,
      marksObtained: 430,
      semesterCredit: 19,
      sgpa: 8.6,
      cumulativeCredit: 39,
      cgpa: 8.5,
      letterGrade: "B+",
      remarks: "Very Good",
    },
    {
      rollNo: 20,
      semester: 2,
      year: 2014,
      fullMarks: 500,
      marksObtained: 420,
      semesterCredit: 18,
      sgpa: 8.4,
      cumulativeCredit: 38,
      cgpa: 8.3,
      letterGrade: "B",
      remarks: "Good",
    },
  ];
  