

import { Payment } from "@/components/reports/types";
import { IssuedBook } from "@/types/resources/IssuedBooks";

export const mockData: Payment[] = [
    {
        "roll": 1,
        "semester": 8,
        "name": "<PERSON><PERSON><PERSON>",
        "year": 2022,
        "fullMarks": 500,
        "marksObtained": 375,
        "semesterCredit": 20,
        "sgpa": 9.74,
        "cumulativeCredit": 55,
        "cgpa": 7.85,
        "letterGrade": "B+",
        "stream": "BCOM"
    },
    {
        "roll": 2,
        "semester": 4,
        "name": "<PERSON><PERSON>",
        "year": 2023,
        "fullMarks": 500,
        "marksObtained": 418,
        "semesterCredit": 17,
        "sgpa": 7.24,
        "cumulativeCredit": 112,
        "cgpa": 6.27,
        "letterGrade": "C",
        "stream": "BA"
    },
    {
        "roll": 3,
        "semester": 8,
        "name": "<PERSON><PERSON><PERSON>",
        "year": 2025,
        "fullMarks": 500,
        "marksObtained": 333,
        "semesterCredit": 17,
        "sgpa": 9.25,
        "cumulativeCredit": 66,
        "cgpa": 7.15,
        "letterGrade": "C",
        "stream": "BSC"
    },
    {
        "roll": 4,
        "semester": 8,
        "name": "Meera Desai",
        "year": 2024,
        "fullMarks": 500,
        "marksObtained": 468,
        "semesterCredit": 18,
        "sgpa": 7.45,
        "cumulativeCredit": 94,
        "cgpa": 8.37,
        "letterGrade": "A+",
        "stream": "BCOM"
    },
    {
        "roll": 5,
        "semester": 3,
        "name": "Aryan Joshi",
        "year": 2023,
        "fullMarks": 500,
        "marksObtained": 319,
        "semesterCredit": 18,
        "sgpa": 6.9,
        "cumulativeCredit": 145,
        "cgpa": 9.06,
        "letterGrade": "B+",
        "stream": "BSC"
    },
    {
        "roll": 6,
        "semester": 3,
        "name": "Diya Iyer",
        "year": 2024,
        "fullMarks": 500,
        "marksObtained": 437,
        "semesterCredit": 19,
        "sgpa": 7.23,
        "cumulativeCredit": 66,
        "cgpa": 8.29,
        "letterGrade": "A",
        "stream": "BSC"
    },
    {
        "roll": 7,
        "semester": 8,
        "name": "Kavya Reddy",
        "year": 2022,
        "fullMarks": 500,
        "marksObtained": 495,
        "semesterCredit": 17,
        "sgpa": 6.63,
        "cumulativeCredit": 74,
        "cgpa": 7.14,
        "letterGrade": "A",
        "stream": "BA"
    },
    {
        "roll": 8,
        "semester": 5,
        "name": "Rohan Singh",
        "year": 2023,
        "fullMarks": 500,
        "marksObtained": 407,
        "semesterCredit": 19,
        "sgpa": 7.63,
        "cumulativeCredit": 123,
        "cgpa": 7.06,
        "letterGrade": "A+",
        "stream": "BSC"
    },
    {
        "roll": 9,
        "semester": 7,
        "name": "Priya Nair",
        "year": 2022,
        "fullMarks": 500,
        "marksObtained": 418,
        "semesterCredit": 18,
        "sgpa": 6.23,
        "cumulativeCredit": 120,
        "cgpa": 7.51,
        "letterGrade": "B+",
        "stream": "BSC"
    },
    {
        "roll": 10,
        "semester": 8,
        "name": "Aditya Mehta",
        "year": 2022,
        "fullMarks": 500,
        "marksObtained": 370,
        "semesterCredit": 16,
        "sgpa": 7.14,
        "cumulativeCredit": 56,
        "cgpa": 6.93,
        "letterGrade": "A",
        "stream": "BSC"
    },
    {
        "roll": 11,
        "semester": 8,
        "name": "Niharika Rao",
        "year": 2023,
        "fullMarks": 500,
        "marksObtained": 470,
        "semesterCredit": 20,
        "sgpa": 8.42,
        "cumulativeCredit": 83,
        "cgpa": 9.39,
        "letterGrade": "A+",
        "stream": "BSC"
    },
    {
        "roll": 12,
        "semester": 3,
        "name": "Siddharth Patel",
        "year": 2025,
        "fullMarks": 500,
        "marksObtained": 438,
        "semesterCredit": 15,
        "sgpa": 7.48,
        "cumulativeCredit": 139,
        "cgpa": 9.31,
        "letterGrade": "B+",
        "stream": "BA"
    },
    {
        "roll": 13,
        "semester": 4,
        "name": "Tanvi Kapoor",
        "year": 2023,
        "fullMarks": 500,
        "marksObtained": 457,
        "semesterCredit": 20,
        "sgpa": 8.39,
        "cumulativeCredit": 54,
        "cgpa": 9.78,
        "letterGrade": "A+",
        "stream": "BCOM"
    },
    {
        "roll": 14,
        "semester": 2,
        "name": "Vivaan Kumar",
        "year": 2022,
        "fullMarks": 500,
        "marksObtained": 318,
        "semesterCredit": 15,
        "sgpa": 8.76,
        "cumulativeCredit": 34,
        "cgpa": 6.25,
        "letterGrade": "C",
        "stream": "BSC"
    },
    {
        "roll": 15,
        "semester": 3,
        "name": "Sanya Arora",
        "year": 2025,
        "fullMarks": 500,
        "marksObtained": 330,
        "semesterCredit": 17,
        "sgpa": 9.53,
        "cumulativeCredit": 105,
        "cgpa": 8.48,
        "letterGrade": "A",
        "stream": "BCOM"
    },
    {
        "roll": 16,
        "semester": 2,
        "name": "Riya Menon",
        "year": 2023,
        "fullMarks": 500,
        "marksObtained": 478,
        "semesterCredit": 20,
        "sgpa": 8.61,
        "cumulativeCredit": 58,
        "cgpa": 9.0,
        "letterGrade": "C",
        "stream": "Very BA"
    },
    {
        "roll": 17,
        "semester": 7,
        "name": "Arjun Roy",
        "year": 2022,
        "fullMarks": 500,
        "marksObtained": 443,
        "semesterCredit": 17,
        "sgpa": 8.52,
        "cumulativeCredit": 139,
        "cgpa": 9.99,
        "letterGrade": "C",
        "stream": "BA"
    },
    {
        "roll": 18,
        "semester": 6,
        "name": "Pooja Chaudhary",
        "year": 2023,
        "fullMarks": 500,
        "marksObtained": 394,
        "semesterCredit": 18,
        "sgpa": 7.15,
        "cumulativeCredit": 143,
        "cgpa": 6.08,
        "letterGrade": "C",
        "stream": "BSC"
    },
    {
        "roll": 19,
        "semester": 8,
        "name": "Karan Malhotra",
        "year": 2022,
        "fullMarks": 500,
        "marksObtained": 340,
        "semesterCredit": 15,
        "sgpa": 9.56,
        "cumulativeCredit": 43,
        "cgpa": 7.42,
        "letterGrade": "C",
        "stream": "Very BA"
    },
    {
        "roll": 20,
        "semester": 1,
        "name": "Shruti Pandey",
        "year": 2024,
        "fullMarks": 500,
        "marksObtained": 500,
        "semesterCredit": 16,
        "sgpa": 6.17,
        "cumulativeCredit": 51,
        "cgpa": 9.73,
        "letterGrade": "C",
        "stream": "BSC"
    },
    {
        "roll": 21,
        "semester": 1,
        "name": "Neha Bansal",
        "year": 2023,
        "fullMarks": 500,
        "marksObtained": 328,
        "semesterCredit": 16,
        "sgpa": 8.13,
        "cumulativeCredit": 59,
        "cgpa": 7.07,
        "letterGrade": "B+",
        "stream": "BSC"
    },
    {
        "roll": 22,
        "semester": 2,
        "name": "Vikas Aggarwal",
        "year": 2022,
        "fullMarks": 500,
        "marksObtained": 328,
        "semesterCredit": 19,
        "sgpa": 7.42,
        "cumulativeCredit": 140,
        "cgpa": 8.33,
        "letterGrade": "C",
        "stream": "BSC"
    },
    {
        "roll": 23,
        "semester": 3,
        "name": "Sakshi Jain",
        "year": 2023,
        "fullMarks": 500,
        "marksObtained": 486,
        "semesterCredit": 19,
        "sgpa": 8.3,
        "cumulativeCredit": 34,
        "cgpa": 6.0,
        "letterGrade": "B+",
        "stream": "BA"
    },
    {
        "roll": 24,
        "semester": 3,
        "name": "Gaurav Mishra",
        "year": 2024,
        "fullMarks": 500,
        "marksObtained": 327,
        "semesterCredit": 20,
        "sgpa": 6.9,
        "cumulativeCredit": 62,
        "cgpa": 8.45,
        "letterGrade": "C",
        "stream": "BA"
    },
    {
        "roll": 25,
        "semester": 2,
        "name": "Akash Yadav",
        "year": 2023,
        "fullMarks": 500,
        "marksObtained": 358,
        "semesterCredit": 20,
        "sgpa": 8.3,
        "cumulativeCredit": 45,
        "cgpa": 7.86,
        "letterGrade": "B",
        "stream": "BSC"
    },
    {
        "roll": 26,
        "semester": 7,
        "name": "Nisha Tiwari",
        "year": 2023,
        "fullMarks": 500,
        "marksObtained": 327,
        "semesterCredit": 15,
        "sgpa": 9.7,
        "cumulativeCredit": 128,
        "cgpa": 7.99,
        "letterGrade": "A+",
        "stream": "BA"
    },
    {
        "roll": 27,
        "semester": 5,
        "name": "Aditi Saxena",
        "year": 2025,
        "fullMarks": 500,
        "marksObtained": 335,
        "semesterCredit": 15,
        "sgpa": 9.32,
        "cumulativeCredit": 72,
        "cgpa": 8.98,
        "letterGrade": "A",
        "stream": "BA"
    },
    {
        "roll": 28,
        "semester": 3,
        "name": "Rahul Kulkarni",
        "year": 2024,
        "fullMarks": 500,
        "marksObtained": 382,
        "semesterCredit": 20,
        "sgpa": 6.93,
        "cumulativeCredit": 133,
        "cgpa": 8.32,
        "letterGrade": "B+",
        "stream": "BSC"
    },
    {
        "roll": 29,
        "semester": 2,
        "name": "Sneha Bhatt",
        "year": 2022,
        "fullMarks": 500,
        "marksObtained": 425,
        "semesterCredit": 16,
        "sgpa": 9.16,
        "cumulativeCredit": 127,
        "cgpa": 6.41,
        "letterGrade": "C",
        "stream": "BCOM"
    },
    {
        "roll": 30,
        "semester": 1,
        "name": "Harsh Tandon",
        "year": 2024,
        "fullMarks": 500,
        "marksObtained": 453,
        "semesterCredit": 18,
        "sgpa": 7.1,
        "cumulativeCredit": 67,
        "cgpa": 7.44,
        "letterGrade": "B",
        "stream": "BSC"
    },
    {
        "roll": 31,
        "semester": 3,
        "name": "Lavanya Tripathi",
        "year": 2022,
        "fullMarks": 500,
        "marksObtained": 399,
        "semesterCredit": 18,
        "sgpa": 9.42,
        "cumulativeCredit": 160,
        "cgpa": 8.23,
        "letterGrade": "B+",
        "stream": "BA"
    },
    {
        "roll": 32,
        "semester": 3,
        "name": "Parth Chawla",
        "year": 2025,
        "fullMarks": 500,
        "marksObtained": 372,
        "semesterCredit": 18,
        "sgpa": 6.84,
        "cumulativeCredit": 149,
        "cgpa": 6.73,
        "letterGrade": "C",
        "stream": "Very BA"
    },
    {
        "roll": 33,
        "semester": 3,
        "name": "Ritika Dutta",
        "year": 2024,
        "fullMarks": 500,
        "marksObtained": 331,
        "semesterCredit": 19,
        "sgpa": 9.68,
        "cumulativeCredit": 41,
        "cgpa": 9.55,
        "letterGrade": "B+",
        "stream": "BSC"
    },
    {
        "roll": 34,
        "semester": 5,
        "name": "Kunal Goswami",
        "year": 2024,
        "fullMarks": 500,
        "marksObtained": 333,
        "semesterCredit": 17,
        "sgpa": 6.6,
        "cumulativeCredit": 108,
        "cgpa": 9.03,
        "letterGrade": "B+",
        "stream": "BSC"
    },
    {
        "roll": 35,
        "semester": 3,
        "name": "Shreya Basu",
        "year": 2025,
        "fullMarks": 500,
        "marksObtained": 457,
        "semesterCredit": 17,
        "sgpa": 7.42,
        "cumulativeCredit": 87,
        "cgpa": 7.67,
        "letterGrade": "C",
        "stream": "Very BA"
    },
    {
        "roll": 36,
        "semester": 1,
        "name": "Manav Kapoor",
        "year": 2022,
        "fullMarks": 500,
        "marksObtained": 382,
        "semesterCredit": 15,
        "sgpa": 8.39,
        "cumulativeCredit": 31,
        "cgpa": 9.95,
        "letterGrade": "B",
        "stream": "BA"
    },
    {
        "roll": 37,
        "semester": 4,
        "name": "Simran Gill",
        "year": 2023,
        "fullMarks": 500,
        "marksObtained": 307,
        "semesterCredit": 18,
        "sgpa": 6.97,
        "cumulativeCredit": 132,
        "cgpa": 9.49,
        "letterGrade": "B+",
        "stream": "BCOM"
    },
    {
        "roll": 38,
        "semester": 3,
        "name": "Pranav Joshi",
        "year": 2024,
        "fullMarks": 500,
        "marksObtained": 468,
        "semesterCredit": 18,
        "sgpa": 9.41,
        "cumulativeCredit": 52,
        "cgpa": 6.86,
        "letterGrade": "C",
        "stream": "BSC"
    },
    {
        "roll": 39,
        "semester": 8,
        "name": "Tanya Chopra",
        "year": 2024,
        "fullMarks": 500,
        "marksObtained": 331,
        "semesterCredit": 19,
        "sgpa": 8.32,
        "cumulativeCredit": 136,
        "cgpa": 8.52,
        "letterGrade": "A+",
        "stream": "BA"
    },
    {
        "roll": 40,
        "semester": 7,
        "name": "Raghav Jain",
        "year": 2022,
        "fullMarks": 500,
        "marksObtained": 390,
        "semesterCredit": 16,
        "sgpa": 7.16,
        "cumulativeCredit": 87,
        "cgpa": 9.6,
        "letterGrade": "B",
        "stream": "BSC"
    },
    {
        "roll": 41,
        "semester": 3,
        "name": "Maya Pillai",
        "year": 2023,
        "fullMarks": 500,
        "marksObtained": 343,
        "semesterCredit": 19,
        "sgpa": 8.61,
        "cumulativeCredit": 74,
        "cgpa": 6.22,
        "letterGrade": "A",
        "stream": "BSC"
    },
    {
        "roll": 42,
        "semester": 5,
        "name": "Krishna Nair",
        "year": 2022,
        "fullMarks": 500,
        "marksObtained": 318,
        "semesterCredit": 16,
        "sgpa": 9.37,
        "cumulativeCredit": 40,
        "cgpa": 9.79,
        "letterGrade": "B",
        "stream": "BSC"
    },
    {
        "roll": 43,
        "semester": 8,
        "name": "Aniket Sengupta",
        "year": 2024,
        "fullMarks": 500,
        "marksObtained": 382,
        "semesterCredit": 16,
        "sgpa": 7.92,
        "cumulativeCredit": 41,
        "cgpa": 9.35,
        "letterGrade": "A+",
        "stream": "Very BA"
    },
    {
        "roll": 44,
        "semester": 2,
        "name": "Anushka Patil",
        "year": 2023,
        "fullMarks": 500,
        "marksObtained": 403,
        "semesterCredit": 19,
        "sgpa": 7.91,
        "cumulativeCredit": 43,
        "cgpa": 8.85,
        "letterGrade": "C",
        "stream": "BSC"
    },
    {
        "roll": 45,
        "semester": 2,
        "name": "Devika Rao",
        "year": 2025,
        "fullMarks": 500,
        "marksObtained": 301,
        "semesterCredit": 19,
        "sgpa": 9.27,
        "cumulativeCredit": 160,
        "cgpa": 9.93,
        "letterGrade": "C",
        "stream": "BSC"
    },
    {
        "roll": 46,
        "semester": 8,
        "name": "Kabir Ahuja",
        "year": 2022,
        "fullMarks": 500,
        "marksObtained": 407,
        "semesterCredit": 15,
        "sgpa": 9.98,
        "cumulativeCredit": 122,
        "cgpa": 6.02,
        "letterGrade": "A+",
        "stream": "BA"
    },
    {
        "roll": 47,
        "semester": 6,
        "name": "Mahima Gupta",
        "year": 2022,
        "fullMarks": 500,
        "marksObtained": 345,
        "semesterCredit": 18,
        "sgpa": 8.1,
        "cumulativeCredit": 34,
        "cgpa": 6.49,
        "letterGrade": "A",
        "stream": "BSC"
    },
    {
        "roll": 48,
        "semester": 2,
        "name": "Nakul Shah",
        "year": 2025,
        "fullMarks": 500,
        "marksObtained": 458,
        "semesterCredit": 20,
        "sgpa": 6.68,
        "cumulativeCredit": 117,
        "cgpa": 8.01,
        "letterGrade": "C",
        "stream": "BSC"
    },
    {
        "roll": 49,
        "semester": 8,
        "name": "Samaira Khanna",
        "year": 2024,
        "fullMarks": 500,
        "marksObtained": 423,
        "semesterCredit": 17,
        "sgpa": 7.34,
        "cumulativeCredit": 58,
        "cgpa": 9.96,
        "letterGrade": "C",
        "stream": "BSC"
    },
    {
        "roll": 50,
        "semester": 1,
        "name": "Omkar Kulkarni",
        "year": 2024,
        "fullMarks": 500,
        "marksObtained": 402,
        "semesterCredit": 19,
        "sgpa": 7.54,
        "cumulativeCredit": 146,
        "cgpa": 9.25,
        "letterGrade": "C",
        "stream": "BCOM"
    },
];


export const IssueBookData: IssuedBook[] = [
    {
      "id": 1,
      "title": "JavaScript Essentials",
      "author": "Mark Smith",
      "isbn": "978-1-234567-89-0",
      "category": "Programming",
      "issueDate": "2024-02-01",
      "dueDate": "2024-02-15",
      "status": "Pending",
      "fine": 0,
      "canRenew": true
    },
    {
      "id": 2,
      "title": "React Mastery",
      "author": "Jane Doe",
      "isbn": "978-1-987654-32-1",
      "category": "Web Development",
      "issueDate": "2024-01-20",
      "dueDate": "2024-02-10",
      "status": "Returned",
      "fine": 0,
      "canRenew": false
    },
    {
      "id": 3,
      "title": "Node.js Guide",
      "author": "John Williams",
      "isbn": "978-1-765432-10-9",
      "category": "Backend Development",
      "issueDate": "2024-01-25",
      "dueDate": "2024-02-08",
      "status": "Pending",
      "fine": 24,
      "canRenew": false
    },
    {
      "id": 4,
      "title": "Design Patterns",
      "author": "Robert C. Martin",
      "isbn": "978-1-654321-09-8",
      "category": "Software Design",
      "issueDate": "2024-01-28",
      "dueDate": "2024-02-12",
      "status": "Returned",
      "fine": 0,
      "canRenew": false
    },
    {
      "id": 5,
      "title": "Python Crash Course",
      "author": "Eric Matthes",
      "isbn": "978-1-543210-98-7",
      "category": "Programming",
      "issueDate": "2024-01-15",
      "dueDate": "2024-01-30",
      "status": "Pending",
      "fine": 42,
      "canRenew": false
    },
    {
      "id": 6,
      "title": "Data Structures & Algorithms",
      "author": "Thomas Cormen",
      "isbn": "978-1-432109-87-6",
      "category": "Computer Science",
      "issueDate": "2024-02-05",
      "dueDate": "2024-02-20",
      "status": "Pending",
      "fine": 0,
      "canRenew": true
    },
    {
      "id": 7,
      "title": "Machine Learning with Python",
      "author": "Sebastian Raschka",
      "isbn": "978-1-321098-76-5",
      "category": "AI & Machine Learning",
      "issueDate": "2024-01-10",
      "dueDate": "2024-01-25",
      "status": "Pending",
      "fine": 54,
      "canRenew": false
    }
  ];
  