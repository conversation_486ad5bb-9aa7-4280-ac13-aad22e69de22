{
  //   "extends": "../../tsconfig.json",
  "compilerOptions": {
    "module": "ESNext", // Use ESNext for ESM
    "target": "ES2020", // Target modern ECMAScript versions
    "moduleResolution": "node",
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "outDir": "./dist", // Output directory for compiled files
    "strict": true, // Enable strict type-checking options
    "skipLibCheck": true, // Skip type checking of declaration files
    "resolveJsonModule": true, // Include JSON imports
    "forceConsistentCasingInFileNames": true,
    "noEmit": false, // Allow emitting output
    "isolatedModules": true, // Required for using ESM modules
    // "baseUrl": ".", // Allow absolute imports relative to project root

    "baseUrl": ".", // Set base URL for resolving paths
    "paths": {
      "@/db/*": ["./src/db/*"], // Explicit path for @/db
      "@/*": ["./src/*"] // General path for other imports
    }
  },
  "include": ["./drizzle.config.js", "./src/**/*", "./src/**/*.js"],
  "exclude": ["dist", "node_modules"]
}
