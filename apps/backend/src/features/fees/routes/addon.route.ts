import { Router } from "express";
import { create<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, delete<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, getAddonById<PERSON><PERSON><PERSON>, getA<PERSON>onsHand<PERSON>, updateAddonHand<PERSON> } from "../controllers/addon.controller.js";
// import { getAddonsHand<PERSON>, getAddonBy<PERSON>d<PERSON><PERSON><PERSON>, createAddon<PERSON><PERSON><PERSON>, update<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, deleteAddon<PERSON>and<PERSON> } from "../controllers/addon.controller.js";

const addonRouter = Router();

addonRouter.get("/", getAddonsHandler);
addonRouter.get("/:id", getAddonByIdHandler);
addonRouter.post("/", createAddonHandler);
addonRouter.put("/:id", updateAddonHandler);
addonRouter.delete("/:id", deleteAddonHandler);

export default addonRouter;
