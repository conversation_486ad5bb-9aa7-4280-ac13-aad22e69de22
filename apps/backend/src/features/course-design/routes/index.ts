import courseRouter from "./course.route.js";
import subjectRouter from "./subject.routes.js";
import subjectTypeRouter from "./subject-type.routes.js";
import paperRouter from "./paper.route.js";
import topicRouter from "./topic.route.js";
import streamRouter from "./stream.routes.js";
import affiliationRouter from "./affiliation.routes.js";
import affiliationTypeRouter from "./affiliation-type.routes.js";
import regulationTypeRouter from "./regulation-type.routes.js";
import programCourseRouter from "./program-course.routes.js";
import courseTypeRouter from "./course-type.route.js";
import courseLevelRouter from "./course-level.routes.js";
import specializationRouter from "./specialization.routes.js";
import examComponentRouter from "./exam-component.routes.js";
import cascadingDropdownsRouter from "./cascading-dropdowns.route.js";

export {
  courseRouter,
  subjectRouter,
  paperRouter,
  topicRouter,
  streamRouter,
  subjectTypeRouter,
  affiliationRouter,
  affiliationTypeRouter,
  regulationTypeRouter,
  programCourseRouter,
  courseTypeRouter,
  courseLevelRouter,
  specializationRouter,
  examComponentRouter,
  cascadingDropdownsRouter,
}; 
