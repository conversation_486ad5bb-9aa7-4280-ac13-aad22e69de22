import { Router } from "express";
import {
  createAffiliationType<PERSON><PERSON><PERSON>,
  deleteAffiliationType<PERSON><PERSON><PERSON>,
  getAllAffiliationTypesHandler,
  getAffiliationTypeByIdHandler,
  updateAffiliationTypeHandler,
  bulkUploadAffiliationTypesHandler
} from "../controllers/affiliation-type.controller.js";
import { RequestHandler } from "express";
import { uploadExcelMiddleware } from "@/middlewares/uploadMiddleware.middleware.js";

const router = Router();

// Affiliation Type routes
router.post("/", createAffiliationTypeHandler as RequestHandler);
router.post("/bulk-upload", uploadExcelMiddleware, bulkUploadAffiliationTypesHandler as RequestHandler);
router.get("/", getAllAffiliationTypesHandler as RequestHandler);
router.get("/:id", getAffiliationTypeByIdHandler as RequestHandler);
router.put("/:id", updateAffiliationType<PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON>and<PERSON>);
router.delete("/:id", deleteAffiliationTypeHand<PERSON> as RequestHandler);

export default router;
