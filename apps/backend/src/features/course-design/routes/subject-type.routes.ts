import { Router } from "express";
import {
  createSubjectType,
  getAllSubjectTypes,
  getSubjectTypeById,
  updateSubjectType,
  deleteSubjectType,
  bulkUploadSubjectTypesHandler
} from "../controllers/subject-type.controller.js";
import { RequestHand<PERSON> } from "express";
import { uploadExcelMiddleware } from "@/middlewares/uploadMiddleware.middleware.js";

const router = Router();

// Subject Type routes
router.post("/", createSubjectType as RequestHandler);
router.post("/bulk-upload", uploadExcelMiddleware, bulkUploadSubjectTypesHandler as <PERSON>questHandler);
router.get("/", getAllSubjectTypes as <PERSON>questHandler);
router.get("/:id", getSubjectTypeById as <PERSON>questHandler);
router.put("/:id", updateSubjectType as RequestHandler);
router.delete("/:id", deleteSubjectType as <PERSON>questHandler);

export default router;
