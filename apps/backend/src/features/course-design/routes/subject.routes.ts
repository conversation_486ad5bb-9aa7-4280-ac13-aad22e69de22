import { Router } from "express";
import {
  createS<PERSON><PERSON><PERSON><PERSON><PERSON>,
  getAllSubjects<PERSON>andler,
  getSubjectByIdHandler,
  updateSubject<PERSON>and<PERSON>,
  deleteSubjectHandler,
} from "../controllers/subject.controller.js";
import { RequestHandler } from "express";

const router = Router();

// Subject routes
router.post("/", createSubjectHandler as RequestHandler);
router.get("/", getAllSubjectsHandler as RequestHandler);
router.get("/:id", getSubjectByIdHandler as RequestHandler);
router.put("/:id", updateSubjectHandler as <PERSON>questHandler);
router.delete("/:id", deleteSubjectHandler as <PERSON>questHandler);

export default router;
