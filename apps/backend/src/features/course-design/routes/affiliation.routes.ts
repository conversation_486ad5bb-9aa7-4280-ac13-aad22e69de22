import { Router } from "express";
import {
  createAffiliation<PERSON><PERSON><PERSON>,
  deleteAffiliation<PERSON><PERSON><PERSON>,
  getAllAffiliationsHandler,
  getAffiliationByIdHandler,
  updateAffiliationHandler,
  bulkUploadAffiliationsHandler
} from "../controllers/affiliation.controller.js";
import { RequestHandler } from "express";
import { uploadExcelMiddleware } from "@/middlewares/uploadMiddleware.middleware.js";

const router = Router();

// Affiliation routes
router.post("/", createAffiliationHandler as RequestHandler);
router.post("/bulk-upload", uploadExcelMiddleware, bulkUploadAffiliationsHandler as RequestHandler);
router.get("/", getAllAffiliationsHandler as RequestHandler);
router.get("/:id", getAffiliationByIdHandler as RequestHandler);
router.put("/:id", updateAffiliationHandler as RequestHandler);
router.delete("/:id", deleteAffiliation<PERSON><PERSON><PERSON> as <PERSON>questHandler);

export default router; 