import { Router } from "express";
import {
  createPaperComponent,
  getAllPaperComponents,
  getPaperComponentById,
  updatePaperComponent,
  deletePaperComponent,
} from "../controllers/paper-component.controller.js";
import { RequestHandler } from "express";

const router = Router();

// Paper Component routes
router.post("/", createPaperComponent as <PERSON>quest<PERSON>andler);
router.get("/", getAllPaperComponents as <PERSON>questHandler);
router.get("/:id", getPaperComponentById as RequestHandler);
router.put("/:id", updatePaperComponent as <PERSON>quest<PERSON>and<PERSON>);
router.delete("/:id", deletePaperComponent as <PERSON><PERSON><PERSON><PERSON>ler);

export default router;
