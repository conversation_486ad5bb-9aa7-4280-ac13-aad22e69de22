import { Router, RequestHand<PERSON> } from "express";
import {
  createRegulationType<PERSON><PERSON><PERSON>,
  deleteRegulationType<PERSON><PERSON><PERSON>,
  getAllRegulationTypes<PERSON>and<PERSON>,
  getRegulationTypeBy<PERSON>d<PERSON>and<PERSON>,
  updateRegulationTypeHandler,
  bulkUploadRegulationTypesHandler
} from "../controllers/regulation-type.controller.js";
import { uploadExcelMiddleware } from "@/middlewares/uploadMiddleware.middleware.js";

const router = Router();

router.post("/", createRegulationTypeHandler as RequestHandler);
router.post("/bulk-upload", uploadExcelMiddleware, bulkUploadRegulationTypesHandler as RequestHandler);
router.get("/", getAllRegulationTypesHandler as RequestHandler);
router.get("/:id", getRegulationTypeByIdHandler as RequestHandler);
router.put("/:id", updateRegulationTypeHandler as <PERSON>questHand<PERSON>);
router.delete("/:id", deleteRegulationTypeHand<PERSON> as <PERSON>quest<PERSON>and<PERSON>);

export default router;
