import bloodGroupRouter from "@/features/resources/routes/bloodGroup.routes.js";
import categoryRouter from "@/features/resources/routes/category.routes.js";
import cityRouter from "@/features/resources/routes/city.routes.js";
import languageMediumRouter from "@/features/resources/routes/languageMedium.routes.js";
import boardUniversityRouter from "@/features/resources/routes/boardUniversity.routes.js";
import institutionRouter from "@/features/resources/routes/institution.routes.js";
import qualificationRouter from "@/features/resources/routes/qualification.routes.js";
import transportRouter from "@/features/resources/routes/transport.routes.js";
import countryRouter from "@/features/resources/routes/country.routes.js";
import degreeRouter from "@/features/resources/routes/degree.routes.js";
import nationalityRouter from "@/features/resources/routes/nationality.routes.js";
import occupationRouter from "@/features/resources/routes/occupation.routes.js";
import pickupPointRouter from "@/features/resources/routes/pickupPoint.routes.js";
import religionRouter from "@/features/resources/routes/religion.routes.js";
import stateRouter from "@/features/resources/routes/state.routes.js";
import annualIncomeRouter from "@/features/resources/routes/annualIncome.routes.js";

export {
    bloodGroupRouter,
    categoryRouter,
    cityRouter,
    languageMediumRouter,
    boardUniversityRouter,
    institutionRouter,
    qualificationRouter,
    transportRouter,
    countryRouter,
    degreeRouter,
    nationalityRouter,
    occupationRouter,
    pickupPointRouter,
    religionRouter,
    stateRouter,
    annualIncomeRouter,
};
