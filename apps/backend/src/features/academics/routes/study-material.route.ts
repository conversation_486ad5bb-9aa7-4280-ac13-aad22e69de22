// import { Router } from "express";
// import * as controller from "../controllers/study-material.controller.js";

// const router = Router();

// router.post("/", controller.createStudyMaterial);
// // router.get("/batch-paper/:batchPaperId", controller.getStudyMaterialByBatchPaperId);
// router.get("/", controller.getAllStudyMaterials);
// router.get("/:id", controller.getStudyMaterialById);
// router.put("/:id", controller.updateStudyMaterial);
// router.delete("/:id", controller.deleteStudyMaterial);

// export default router;
