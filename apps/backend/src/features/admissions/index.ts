// Export all admissions controllers and routes (to be created)
export { default as admissionRouter } from "./routes/admission.route.js";
export { default as applicationFormRouter } from "./routes/application-form.route.js";
export { default as admissionGeneralInfoRouter } from "./routes/admission-general-info.route.js";
export { default as admissionAcademicInfoRouter } from "./routes/admission-academic-info.route.js";
export { default as admissionAdditionalInfoRouter } from "./routes/admission-additional-info.route.js";
export { default as admissionCourseRouter } from "./routes/admission-course.route.js";
export { default as admissionCourseApplicationRouter } from "./routes/admission-course-application.route.js";
export { default as sportsCategoryRouter } from "./routes/sports-category.route.js";
export { default as sportsInfoRouter } from "./routes/sports-info.route.js";
export { default as studentAcademicSubjectRouter } from "./routes/student-academic-subject.route.js";
export { default as academicSubjectRouter } from "./routes/academic-subject.route.js"; 