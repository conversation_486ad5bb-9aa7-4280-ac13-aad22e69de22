# NODE_ENV= production | development
NODE_ENV=
PORT=8080

DEVELOPER_EMAIL=
DEVELOPER_PHONE=

DATABASE_URL=postgresql://<username>:<password>@<host>:<port>/<database_name>

OLD_DB_HOST=
OLD_DB_PORT=
OLD_DB_USER=
OLD_DB_NAME=
OLD_DB_PASSWORD=

CORS_ORIGIN=http://localhost:5173

ACCESS_TOKEN_SECRET=
ACCESS_TOKEN_EXPIRY=1h

REFRESH_TOKEN_SECRET=
REFRESH_TOKEN_EXPIRY=1d

GOOGLE_CLIENT_ID=
GOOGLE_CLIENT_SECRET=

DOCUMENT_BASE_PATH=
STUDY_MATERIAL_BASE_PATH=

INTERAKT_API_KEY=
INTERAKT_BASE_URL=

ZEPTO_URL=
ZEPTO_FROM=
ZEPTO_TOKEN=