* {
    margin: 0;
    padding: 0;
}

html {
    font-family:
        system-ui,
        -apple-system,
        BlinkMacSystemFont,
        "Segoe UI",
        Roboto,
        Oxygen,
        Ubuntu,
        Cantarell,
        "Open Sans",
        "Helvetica Neue",
        sans-serif;
}

h1 {
    font-size: 64px;
    margin: 12px 0;
}

body {
    color: white;
    background-color: black;
    width: 100vw;
    height: 100vh;
}

.container {
    width: 75%;
    padding: 12px;
    padding-top: 24px;
    text-align: center;
    font-size: 18px;
    height: 100%;
}

.my-2 {
    margin: 8px 0;
}

.pt-5 {
    padding: 40px 0 0 0;
}

.pb-2 {
    padding: 0 0 16px 0;
}

.gap-2 {
    gap: 2rem;
}

.d-flex {
    display: flex;
}

.justify-content-center {
    justify-content: center;
}

.align-items-center {
    align-items: center;
}

.flex-column {
    flex-direction: column;
}

.border-b {
    border-bottom: 1px solid white;
    width: 100%;
}